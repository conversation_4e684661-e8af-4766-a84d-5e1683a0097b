#!/usr/bin/env python3

import os
from pathlib import Path
from dotenv import load_dotenv

print("Current working directory:", os.getcwd())
print("Script location:", __file__)
print("Script directory:", Path(__file__).parent)

# Test direct loading
load_dotenv()
print("\nEnvironment variables after load_dotenv():")
print("PROJECT_NAME:", os.getenv('PROJECT_NAME'))
print("POSTGRES_SERVER:", os.getenv('POSTGRES_SERVER'))
print("POSTGRES_USER:", os.getenv('POSTGRES_USER'))

# Test pydantic settings
try:
    from app.core.config import Settings
    print("\nTrying to create Settings instance...")
    settings = Settings()
    print("Settings created successfully!")
    print("PROJECT_NAME:", settings.PROJECT_NAME)
    print("POSTGRES_SERVER:", settings.POSTGRES_SERVER)
except Exception as e:
    print("Error creating Settings:", e)
