"""Add timestamp and soft delete fields to user table

Revision ID: a3d16e73aae8
Revises: 1a31ce608336
Create Date: 2025-08-01 15:43:37.430676

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a3d16e73aae8'
down_revision = '1a31ce608336'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # 添加时间戳字段，先设为可空
    op.add_column('user', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('user', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.add_column('user', sa.Column('deleted_at', sa.DateTime(), nullable=True))

    # 为现有记录设置默认时间戳
    op.execute("UPDATE \"user\" SET created_at = NOW(), updated_at = NOW() WHERE created_at IS NULL")

    # 将字段设为非空（除了 deleted_at）
    op.alter_column('user', 'created_at', nullable=False)
    op.alter_column('user', 'updated_at', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'deleted_at')
    op.drop_column('user', 'updated_at')
    op.drop_column('user', 'created_at')
    # ### end Alembic commands ###
