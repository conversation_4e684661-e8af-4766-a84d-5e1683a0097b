#!/usr/bin/env python3
"""测试修改后的物品路由。"""

import json
import os
import sys
from fastapi.testclient import TestClient

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.main import app


def get_access_token(client: TestClient) -> str:
    """获取访问令牌。"""
    login_data = {
        "username": "<EMAIL>",
        "password": "changethis"
    }
    
    response = client.post("/api/v1/login/access-token", data=login_data)
    if response.status_code == 200:
        token_data = response.json()
        return token_data["access_token"]
    else:
        raise Exception(f"登录失败: {response.json()}")


def test_items_routes():
    """测试物品路由的统一响应格式。"""
    print("🧪 测试物品路由的统一响应格式...")
    
    client = TestClient(app)
    
    # 获取访问令牌
    try:
        access_token = get_access_token(client)
        headers = {"Authorization": f"Bearer {access_token}"}
        print("   ✅ 成功获取访问令牌")
    except Exception as e:
        print(f"   ❌ 获取访问令牌失败: {e}")
        return
    
    # 1. 测试创建物品
    print("\n1. 测试创建物品...")
    item_data = {
        "title": "测试物品",
        "description": "这是一个测试物品的描述"
    }
    
    response = client.post("/api/v1/items/", json=item_data, headers=headers)
    print(f"   状态码: {response.status_code}")
    print(f"   追踪ID: {response.headers.get('X-Trace-ID')}")
    
    if response.status_code == 201:
        result = response.json()
        print(f"   响应格式: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 验证统一响应格式
        assert "code" in result, "响应中应包含 code 字段"
        assert "message" in result, "响应中应包含 message 字段"
        assert "data" in result, "响应中应包含 data 字段"
        assert result["code"] == 20100, f"创建成功的 code 应为 20100，实际为 {result['code']}"
        
        item_id = result["data"]["id"]
        print(f"   ✅ 物品创建成功，ID: {item_id}")
        print(f"   ✅ 响应格式符合统一标准")
    else:
        print(f"   ❌ 物品创建失败: {response.json()}")
        return
    
    # 2. 测试获取物品详情
    print("\n2. 测试获取物品详情...")
    response = client.get(f"/api/v1/items/{item_id}", headers=headers)
    print(f"   状态码: {response.status_code}")
    print(f"   追踪ID: {response.headers.get('X-Trace-ID')}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   响应格式: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 验证统一响应格式
        assert "code" in result, "响应中应包含 code 字段"
        assert "message" in result, "响应中应包含 message 字段"
        assert "data" in result, "响应中应包含 data 字段"
        assert result["code"] == 10000, f"成功的 code 应为 10000，实际为 {result['code']}"
        
        print(f"   ✅ 获取物品详情成功")
        print(f"   ✅ 响应格式符合统一标准")
    else:
        print(f"   ❌ 获取物品详情失败: {response.json()}")
    
    # 3. 测试获取物品列表
    print("\n3. 测试获取物品列表...")
    params = {
        "page": 1,
        "page_size": 5
    }
    
    response = client.get("/api/v1/items/", params=params, headers=headers)
    print(f"   状态码: {response.status_code}")
    print(f"   追踪ID: {response.headers.get('X-Trace-ID')}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   响应格式: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 验证统一响应格式
        assert "code" in result, "响应中应包含 code 字段"
        assert "message" in result, "响应中应包含 message 字段"
        assert "data" in result, "响应中应包含 data 字段"
        assert result["code"] == 10000, f"成功的 code 应为 10000，实际为 {result['code']}"
        
        # 验证分页数据格式
        data = result["data"]
        assert "items" in data, "data 中应包含 items 字段"
        assert "total" in data, "data 中应包含 total 字段"
        assert "page" in data, "data 中应包含 page 字段"
        assert "page_size" in data, "data 中应包含 page_size 字段"
        assert "pages" in data, "data 中应包含 pages 字段"
        
        print(f"   ✅ 获取物品列表成功，共 {data['total']} 个物品")
        print(f"   ✅ 分页格式符合统一标准")
    else:
        print(f"   ❌ 获取物品列表失败: {response.json()}")
    
    # 4. 测试更新物品
    print("\n4. 测试更新物品...")
    update_data = {
        "title": "更新后的测试物品",
        "description": "这是更新后的物品描述"
    }
    
    response = client.put(f"/api/v1/items/{item_id}", json=update_data, headers=headers)
    print(f"   状态码: {response.status_code}")
    print(f"   追踪ID: {response.headers.get('X-Trace-ID')}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   响应格式: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 验证统一响应格式
        assert "code" in result, "响应中应包含 code 字段"
        assert "message" in result, "响应中应包含 message 字段"
        assert "data" in result, "响应中应包含 data 字段"
        assert result["code"] == 20000, f"更新成功的 code 应为 20000，实际为 {result['code']}"
        
        print(f"   ✅ 物品更新成功")
        print(f"   ✅ 响应格式符合统一标准")
    else:
        print(f"   ❌ 物品更新失败: {response.json()}")
    
    # 5. 测试删除物品
    print("\n5. 测试删除物品...")
    response = client.delete(f"/api/v1/items/{item_id}", headers=headers)
    print(f"   状态码: {response.status_code}")
    print(f"   追踪ID: {response.headers.get('X-Trace-ID')}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   响应格式: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 验证统一响应格式
        assert "code" in result, "响应中应包含 code 字段"
        assert "message" in result, "响应中应包含 message 字段"
        assert result["code"] == 20200, f"删除成功的 code 应为 20200，实际为 {result['code']}"
        
        print(f"   ✅ 物品删除成功")
        print(f"   ✅ 响应格式符合统一标准")
    else:
        print(f"   ❌ 物品删除失败: {response.json()}")
    
    # 6. 测试错误情况
    print("\n6. 测试错误情况...")
    response = client.get(f"/api/v1/items/{item_id}", headers=headers)
    print(f"   状态码: {response.status_code}")
    
    if response.status_code == 400:
        result = response.json()
        print(f"   错误响应格式: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 验证错误响应格式
        assert "code" in result, "错误响应中应包含 code 字段"
        assert "message" in result, "错误响应中应包含 message 字段"
        
        print(f"   ✅ 错误响应格式符合统一标准")
    else:
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")


def test_response_format_comparison():
    """对比修改前后的响应格式。"""
    print("\n📊 响应格式对比:")
    
    print("\n修改前的响应格式:")
    print("""
    {
        "data": [...],
        "count": 10
    }
    """)
    
    print("\n修改后的统一响应格式:")
    print("""
    {
        "code": 10000,
        "message": "获取物品列表成功",
        "data": {
            "items": [...],
            "total": 10,
            "page": 1,
            "page_size": 10,
            "pages": 1
        },
        "timestamp": "2025-07-31T15:30:00.000000"
    }
    """)
    
    print("\n✅ 主要改进:")
    print("   1. 添加了统一的 code 和 message 字段")
    print("   2. 规范化了分页数据结构")
    print("   3. 添加了时间戳字段")
    print("   4. 使用了中文友好的错误消息")
    print("   5. 统一了所有CRUD操作的响应格式")


def main():
    """主测试函数。"""
    print("🚀 开始测试物品路由的统一响应格式...")
    print("=" * 60)
    
    try:
        test_items_routes()
        test_response_format_comparison()
        
        print("\n" + "=" * 60)
        print("🎉 物品路由统一响应格式测试完成！")
        
        print("\n✅ 成功实现的功能:")
        print("   1. 统一的响应格式（code, message, data）")
        print("   2. 标准化的分页结构")
        print("   3. 中文友好的消息提示")
        print("   4. 完整的CRUD操作支持")
        print("   5. 权限控制和错误处理")
        print("   6. 日志装饰器支持（暂时禁用）")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
