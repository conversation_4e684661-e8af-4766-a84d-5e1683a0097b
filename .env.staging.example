# 测试环境配置示例

# Domain
DOMAIN=staging.yourdomain.com
FRONTEND_HOST=https://staging.yourdomain.com

# Environment
ENVIRONMENT=staging

# Logging Configuration - 测试环境推荐设置
# 日志级别: 测试环境建议使用 INFO 级别
LOG_LEVEL=INFO
# 测试环境可以启用详细日志用于调试
LOG_REQUEST_DETAILS=true
# 测试环境可以记录函数参数用于调试
LOG_FUNCTION_PARAMS=true

PROJECT_NAME="Your Staging Project"
STACK_NAME=your-staging-stack

# Backend
BACKEND_CORS_ORIGINS="https://staging.yourdomain.com"
SECRET_KEY=your-staging-secret-key
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=your-staging-password

# Emails
SMTP_HOST=smtp.yourmailprovider.com
SMTP_USER=your-staging-smtp-user
SMTP_PASSWORD=your-staging-smtp-password
EMAILS_FROM_EMAIL=<EMAIL>
SMTP_TLS=True
SMTP_SSL=False
SMTP_PORT=587

# Postgres
POSTGRES_SERVER=your-staging-db-server
POSTGRES_PORT=5432
POSTGRES_DB=your_staging_db
POSTGRES_USER=your_staging_db_user
POSTGRES_PASSWORD=your-staging-db-password

SENTRY_DSN=your-staging-sentry-dsn

# Docker
DOCKER_IMAGE_BACKEND=your-registry/backend:staging
DOCKER_IMAGE_FRONTEND=your-registry/frontend:staging
