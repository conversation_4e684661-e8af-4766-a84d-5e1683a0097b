# 生产环境配置示例

# Domain
DOMAIN=yourdomain.com
FRONTEND_HOST=https://yourdomain.com

# Environment
ENVIRONMENT=production

# Logging Configuration - 生产环境专业配置
# 日志级别: 生产环境建议使用 INFO 或 WARNING
LOG_LEVEL=INFO
# 生产环境不建议记录详细的请求/响应信息（隐私和性能考虑）
LOG_REQUEST_DETAILS=false
# 生产环境不建议记录函数参数（性能考虑）
LOG_FUNCTION_PARAMS=false
# 生产环境使用JSON格式便于日志聚合分析
LOG_FORMAT=json
# 启用日志文件输出
LOG_FILE_ENABLED=true
# 生产环境日志文件可以更大
LOG_FILE_MAX_SIZE=50
# 保留更多备份文件
LOG_FILE_BACKUP_COUNT=30

PROJECT_NAME="Your Production Project"
STACK_NAME=your-production-stack

# Backend
BACKEND_CORS_ORIGINS="https://yourdomain.com"
SECRET_KEY=your-super-secret-key-here
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=your-secure-password

# Emails
SMTP_HOST=smtp.yourmailprovider.com
SMTP_USER=your-smtp-user
SMTP_PASSWORD=your-smtp-password
EMAILS_FROM_EMAIL=<EMAIL>
SMTP_TLS=True
SMTP_SSL=False
SMTP_PORT=587

# Postgres
POSTGRES_SERVER=your-db-server
POSTGRES_PORT=5432
POSTGRES_DB=your_production_db
POSTGRES_USER=your_db_user
POSTGRES_PASSWORD=your-secure-db-password

SENTRY_DSN=your-sentry-dsn-here

# Docker
DOCKER_IMAGE_BACKEND=your-registry/backend:latest
DOCKER_IMAGE_FRONTEND=your-registry/frontend:latest
